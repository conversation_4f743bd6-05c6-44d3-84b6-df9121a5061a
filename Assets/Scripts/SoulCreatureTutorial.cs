using UnityEngine;
using System.Collections.Generic;
using FMODUnity;
using FMOD.Studio;
using System.Collections;

/// <summary>
/// Simplified version of SoulCreatureLogic for a tutorial creature that appears below the ocean.
/// This script removes random wandering behavior but maintains audio and visual effects.
/// </summary>
public class SoulCreatureTutorial : MonoBehaviour, ISoulCreatureBoostProvider
{
    #region Core Components and References

    // Core references
    private Transform playerTransform;
    private PlayerController playerController;
    private ParticleSystem ps;
    private Transform soulCreatureGlow;

    // Boundary values for movement constraints
    [HideInInspector] public float minY;
    [HideInInspector] public float maxY;

    [Header("Position Constraints")]
    [Tooltip("Maximum distance from player in XZ plane before teleporting closer")]
    [SerializeField] private float maxXZDistanceFromPlayer = 30f;

    [Tooltip("Maximum Y distance from player to check XZ constraints (optimization)")]
    [SerializeField] private float maxYDistanceForConstraints = 30f;

    [Tooltip("Whether to enable position constraints")]
    [SerializeField] private bool enablePositionConstraints = true;

    // Public access to average particle position
    [HideInInspector] public Vector3 averageParticlePosition;
    [HideInInspector] public bool isCollidingWithPlayer = false;

    #endregion

    #region Module Toggles

    [Header("Module Toggles")]
    [Tooltip("Enable/disable particle following behavior")]
    public bool enableParticleFollowing = true;

    [Tooltip("Enable/disable audio functionality")]
    public bool enableAudio = true;

    [Tooltip("Enable/disable player movement affecting this object")]
    public bool enablePlayerMovementEffects = true;

    [Tooltip("Enable/disable collider functionality for player interaction")]
    public bool enableCollider = true;

    #endregion

    #region Particle Following Settings

    [Header("Particle Following Settings")]
    [Tooltip("Minimum speed particles can move at")]
    [SerializeField, Range(1f, 10f)] private float minSpeed = 1f;

    [Tooltip("Maximum speed particles can move at")]
    [SerializeField, Range(5f, 50f)] private float maxSpeed = 10f;

    [Tooltip("Rate at which particle speed interpolates to desired speed")]
    [SerializeField, Range(0.1f, 5f)] private float lerpFactor = 2f;

    [Tooltip("Distance from target where particles start following")]
    [SerializeField, Range(0.5f, 2f)] private float followThreshold = 1f;

    [Tooltip("Distance from target where particles stop following")]
    [SerializeField, Range(0.05f, 0.5f)] private float stopThreshold = 0.3f;

    [Tooltip("Distance at which speed scaling reaches its maximum effect")]
    [SerializeField, Range(5f, 20f)] private float maxDistance = 5f;

    // Particle following internal variables
    private Mesh shapeMesh;
    private readonly Dictionary<uint, ParticleData> particlesData = new();
    private ParticleSystem.Particle[] particlesArray;
    private float[] triangleAreas;
    private float totalArea;

    private struct ParticleData
    {
        public Vector3 TargetLocalPos;
        public float DesiredSpeed;
        public float CurrentSpeed;
        public float Timer;
        public bool IsFollowing;
    }

    #endregion

    #region Player Attraction Settings

    [Header("Player Attraction Settings")]
    [Tooltip("Speed of attraction towards the player")]
    [SerializeField] private float attractionSpeed = 2.0f;

    [Header("Flight Boost")]
    [Tooltip("Amount of flight boost this soul creature provides to the player")]
    [SerializeField] public float flightBoostValue = 6f;

    [Header("Speed Boost")]
    [Tooltip("Amount of speed boost this soul creature provides to the player")]
    [SerializeField] public float speedBoostValue = 3f;

    [Header("Performance")]
    [Tooltip("Maximum deltaTime to prevent large jumps during FPS drops")]
    [SerializeField] private float maxDeltaTime = 0.05f;

    #endregion

    #region Audio Settings

    [Header("Audio Settings")]
    [Tooltip("Primary ambient sound event")]
    [SerializeField] public EventReference sound1;

    [Tooltip("Secondary collision sound event")]
    [SerializeField] public EventReference sound2;

    [Tooltip("Cooldown between collision sounds")]
    [SerializeField] private float collisionCooldown = 0.05f;

    [Header("Sound 1 Settings")]
    [Tooltip("Minimum interval between ambient sounds")]
    [SerializeField] private float minSound1Interval = 1.2f;

    [Tooltip("Maximum interval between ambient sounds")]
    [SerializeField] private float maxSound1Interval = 1.8f;

    [Tooltip("Stagger time for initial sound playback")]
    [SerializeField] private float firstPlayStagger = 0.02f;

    // Audio internal variables
    private List<Vector4> customDataList;
    private List<ParticleCollisionEvent> collisionEvents;
    private float lastCollisionSoundTime = -Mathf.Infinity;
    private bool needsInitialDistribution = true;

    #endregion

    #region Player Movement Effects Settings

    [Header("Effect Range")]
    [Tooltip("Minimum range for player movement effects")]
    [SerializeField] private float minRange = 0.5f;

    [Tooltip("Maximum range for player movement effects")]
    [SerializeField] private float maxRange = 5.0f;

    [Header("Movement Settings")]
    [Tooltip("Minimum affected speed rate")]
    [SerializeField] private float speedAffectedRateMin = 5.0f;

    [Tooltip("Maximum affected speed rate")]
    [SerializeField] private float speedAffectedRateMax = 15.0f;

    [Tooltip("Current affected speed")]
    [SerializeField] private float currentAffectedSpeed = 5.0f;

    [Header("Player Movement Effects Tuning")]
    [Tooltip("How sensitive the soul creature is to the player moving toward it (dot product threshold, -1 to 1). Higher = less sensitive, lower = more sensitive. Typical: 0.2")]
    [SerializeField, Range(-1f, 1f)] private float playerApproachSensitivity = 0.2f;

    // Player movement effects internal variables
    private float targetAffectedSpeed;
    private float speedLerpTimer;
    private float speedChangeTimer;
    private float speedLerpDuration;

    #endregion

    #region Unity Lifecycle Methods

    // Coroutine to properly position the object after a frame
    private IEnumerator PositionAfterDelay(Vector3 targetPosition)
    {
        // Wait for one frame
        yield return null;

        // Move to the correct position
        transform.position = targetPosition;

        // Enable any child objects that might have been disabled
        if (soulCreatureGlow != null)
        {
            soulCreatureGlow.gameObject.SetActive(true);
        }
    }

    private void Awake()
    {
        // Store the intended spawn position
        Vector3 intendedPosition = transform.position;

        // Move far away to prevent the visual glitch
        transform.position = new Vector3(10000f, 10000f, 10000f);

        // Find the glow child object
        soulCreatureGlow = transform.Find("SoulCreature_Glow");

        // Disable the glow object initially
        if (soulCreatureGlow != null)
        {
            soulCreatureGlow.gameObject.SetActive(false);
        }

        if (enableParticleFollowing)
        {
            InitializeParticleSystem();
        }

        // Start coroutine to position correctly after a frame
        StartCoroutine(PositionAfterDelay(intendedPosition));
    }

    private void Start()
    {
        // Get references from GameManager
        if (GameManager.Instance != null)
        {
            playerTransform = GameManager.Instance.player.transform;
            playerController = GameManager.Instance.player;
        }
        else
        {
            Debug.LogError("GameManager instance not found!");
            enabled = false;
            return;
        }

        // Initialize particle system
        ps = GetComponent<ParticleSystem>();
        if (ps == null && (enableParticleFollowing || enableAudio))
        {
            Debug.LogError("No ParticleSystem found on this GameObject!");
            enabled = false;
            return;
        }

        // Initialize boundaries for below ocean
        minY = 0f; // Ground level
        maxY = GameManager.Instance.oceanBottom.position.y - 1.0f;

        // Initialize particle following
        if (enableParticleFollowing)
        {
            particlesArray = new ParticleSystem.Particle[ps.main.maxParticles];
            PrecomputeTriangleAreas();
        }

        // Initialize audio
        if (enableAudio)
        {
            customDataList = new List<Vector4>(ps.main.maxParticles);
            collisionEvents = new List<ParticleCollisionEvent>();
        }

        // Initialize player movement effects
        if (enablePlayerMovementEffects)
        {
            targetAffectedSpeed = speedAffectedRateMin;
            currentAffectedSpeed = speedAffectedRateMin;
            speedLerpTimer = 0f;
            speedChangeTimer = 0f;
            speedLerpDuration = 1f;
        }
    }

    private void Update()
    {
        if (playerTransform == null || playerController == null) return;

        // Update particle following
        if (enableParticleFollowing && ps != null && shapeMesh != null)
        {
            averageParticlePosition = GetAverageParticlePosition();
            UpdateParticles();
        }

        // Apply position constraints if enabled
        if (enablePositionConstraints)
        {
            EnforcePositionConstraints();
        }

        // Update player attraction
        UpdatePlayerAttraction();

        // Update audio
        if (enableAudio && ps != null)
        {
            UpdateAudio();
        }

        // Update player movement effects
        if (enablePlayerMovementEffects)
        {
            UpdatePlayerMovementEffects();
        }
    }

    /// <summary>
    /// Enforces position constraints to keep the SoulCreatureTutorial within a maximum distance from the player.
    /// </summary>
    private void EnforcePositionConstraints()
    {
        // Skip if constraints are disabled
        if (!enablePositionConstraints) return;

        // Get Y distance from player (optimization check)
        float yDistance = Mathf.Abs(transform.position.y - playerTransform.position.y);

        // Skip constraint check if Y distance is too large (optimization)
        if (yDistance > maxYDistanceForConstraints) return;

        // Calculate XZ distance (ignoring Y)
        Vector2 playerXZ = new Vector2(playerTransform.position.x, playerTransform.position.z);
        Vector2 creatureXZ = new Vector2(transform.position.x, transform.position.z);
        float xzDistance = Vector2.Distance(playerXZ, creatureXZ);

        // If distance exceeds the maximum, teleport closer to the player
        if (xzDistance > maxXZDistanceFromPlayer)
        {
            // Calculate direction from player to creature in XZ plane (to preserve relative direction)
            Vector2 direction = (creatureXZ - playerXZ).normalized;

            // Calculate new position at maximum allowed distance
            Vector2 newXZ = playerXZ + direction * maxXZDistanceFromPlayer;

            // Create new position, preserving Y coordinate
            Vector3 newPosition = new Vector3(newXZ.x, transform.position.y, newXZ.y);

            // Apply the new position
            transform.position = newPosition;

            // Debug log
//            Debug.Log($"SoulCreatureTutorial teleported closer to player. Distance was: {xzDistance}, now: {maxXZDistanceFromPlayer}");
        }
    }

    private void LateUpdate()
    {
        if (enableParticleFollowing && soulCreatureGlow != null)
        {
            UpdateSoulCreatureGlow();
        }
    }

    private void OnParticleCollision(GameObject other)
    {
        if (enableAudio && other.CompareTag("Player"))
        {
            HandleAudioCollision(other);
        }
    }

    #endregion

    #region ISoulCreatureBoostProvider Implementation

    public float GetFlightBoostValue() => flightBoostValue;
    public float GetSpeedBoostValue() => speedBoostValue;

    #endregion

    #region Particle Following Implementation

    private void InitializeParticleSystem()
    {
        ps = GetComponent<ParticleSystem>();
        if (ps.shape.shapeType != ParticleSystemShapeType.Mesh || (shapeMesh = ps.shape.mesh) == null)
        {
            Debug.LogError($"Particle System on {gameObject.name} must use a Mesh shape with a valid mesh assigned.");
            enableParticleFollowing = false;
            return;
        }
        particlesArray = new ParticleSystem.Particle[ps.main.maxParticles];
    }

    private void PrecomputeTriangleAreas()
    {
        if (shapeMesh == null) return;

        int triangleCount = shapeMesh.triangles.Length / 3;
        triangleAreas = new float[triangleCount];
        totalArea = 0f;

        for (int i = 0; i < triangleCount; i++)
        {
            int i0 = shapeMesh.triangles[i * 3];
            int i1 = shapeMesh.triangles[i * 3 + 1];
            int i2 = shapeMesh.triangles[i * 3 + 2];

            Vector3 v0 = shapeMesh.vertices[i0];
            Vector3 v1 = shapeMesh.vertices[i1];
            Vector3 v2 = shapeMesh.vertices[i2];

            float area = Vector3.Cross(v1 - v0, v2 - v0).magnitude * 0.5f;
            triangleAreas[i] = area;
            totalArea += area;
        }
    }

    private ParticleData InitializeParticleData()
    {
        Vector3 targetLocalPos = GetRandomPointOnMesh();
        float desiredSpeed = Random.Range(minSpeed, maxSpeed);

        return new ParticleData
        {
            TargetLocalPos = targetLocalPos,
            DesiredSpeed = desiredSpeed,
            CurrentSpeed = desiredSpeed,
            Timer = Random.Range(0f, 1f),
            IsFollowing = false
        };
    }

    private Vector3 GetRandomPointOnMesh()
    {
        if (shapeMesh == null || triangleAreas == null || triangleAreas.Length == 0)
            return Vector3.zero;

        float randomValue = Random.Range(0f, totalArea);
        float currentSum = 0f;
        int selectedTriangle = 0;

        for (int i = 0; i < triangleAreas.Length; i++)
        {
            currentSum += triangleAreas[i];
            if (randomValue <= currentSum)
            {
                selectedTriangle = i;
                break;
            }
        }

        int i0 = shapeMesh.triangles[selectedTriangle * 3];
        int i1 = shapeMesh.triangles[selectedTriangle * 3 + 1];
        int i2 = shapeMesh.triangles[selectedTriangle * 3 + 2];

        Vector3 v0 = shapeMesh.vertices[i0];
        Vector3 v1 = shapeMesh.vertices[i1];
        Vector3 v2 = shapeMesh.vertices[i2];

        float r1 = Random.value;
        float r2 = Random.value;

        if (r1 + r2 > 1f)
        {
            r1 = 1f - r1;
            r2 = 1f - r2;
        }

        return v0 + r1 * (v1 - v0) + r2 * (v2 - v0);
    }

    private void UpdateParticleSpeed(ref ParticleData data)
    {
        float clampedDeltaTime = Mathf.Min(Time.deltaTime, maxDeltaTime);
        data.Timer -= clampedDeltaTime;
        if (data.Timer <= 0f)
        {
            data.DesiredSpeed = Random.Range(minSpeed, maxSpeed);
            data.Timer = Random.Range(1f, 3f);
        }

        data.CurrentSpeed = Mathf.Lerp(data.CurrentSpeed, data.DesiredSpeed, lerpFactor * clampedDeltaTime);
    }

    private void UpdateParticles()
    {
        int count = ps.GetParticles(particlesArray);
        for (int i = 0; i < count; i++)
        {
            ref ParticleSystem.Particle particle = ref particlesArray[i];
            uint id = particle.randomSeed;

            if (!particlesData.TryGetValue(id, out ParticleData data))
            {
                data = InitializeParticleData();
                particlesData[id] = data;
            }

            UpdateParticleSpeed(ref data);
            Vector3 targetWorldPos = transform.TransformPoint(data.TargetLocalPos);
            float distance = Vector3.Distance(particle.position, targetWorldPos);

            data.IsFollowing = data.IsFollowing ? distance >= stopThreshold : distance > followThreshold;

            if (data.IsFollowing)
            {
                float speed = data.CurrentSpeed * Mathf.Min(distance / maxDistance, 1f);

                if (distance >= maxDistance)
                {
                    float speedScaleFactor = Mathf.Max(distance / maxDistance, 1);
                    speed = (data.CurrentSpeed + 10) * speedScaleFactor;
                }
                float clampedDeltaTime = Mathf.Min(Time.deltaTime, maxDeltaTime);
                particle.position = Vector3.MoveTowards(particle.position, targetWorldPos, speed * clampedDeltaTime);
            }

            particlesData[id] = data;
        }
        ps.SetParticles(particlesArray, count);
    }

    private Vector3 GetAverageParticlePosition()
    {
        if (ps == null) return transform.position;

        int count = ps.GetParticles(particlesArray);
        if (count == 0) return transform.position;

        Vector3 sum = Vector3.zero;
        for (int i = 0; i < count; i++)
        {
            sum += particlesArray[i].position;
        }
        return sum / count;
    }

    private void UpdateSoulCreatureGlow()
    {
        if (soulCreatureGlow == null)
        {
            return;
        }

        // Maintain the y position at -0.99f
        Vector3 averagePosition = averageParticlePosition;
        averagePosition.y = -0.999f;
        soulCreatureGlow.position = averagePosition;

        // Reset rotation to none
        soulCreatureGlow.rotation = Quaternion.identity;

        // Use the average y position of the particles for scaling and intensity
        float averageYPosition = averageParticlePosition.y;

        float scaleMin = 0f;
        float scaleMax = 0.7f;
        float yMin = 0.25f;
        float yMax = 3.0f;

        // Map averageYPosition from range [yMin, yMax] to scale range [scaleMax, scaleMin]
        float clampedY = Mathf.Clamp(averageYPosition, yMin, yMax);
        float desiredScale = Mathf.Lerp(scaleMax, scaleMin, (clampedY - yMin) / (yMax - yMin));

        // Apply the desired scale
        soulCreatureGlow.localScale = new Vector3(desiredScale, desiredScale, desiredScale);

        // Map averageYPosition to tint intensity range [1, 0.5] (fully bright to dim)
        float desiredIntensity = Mathf.Lerp(1f, 0f, (clampedY - yMin) / (yMax - yMin));

        // Apply the desired intensity to the material's tint color
        Renderer renderer = soulCreatureGlow.GetComponent<Renderer>();
        if (renderer != null)
        {
            Color tintColor = renderer.material.GetColor("_TintColor"); // Use the correct property for the shader
            tintColor.a = desiredIntensity; // Set the alpha to the desired intensity
            renderer.material.SetColor("_TintColor", tintColor); // Apply the modified color back to the material
        }
    }

    #endregion

    #region Player Attraction Implementation

    private void UpdatePlayerAttraction()
    {
        if (playerTransform == null || ps == null) return;

        // Calculate the minimum distance from the player to any particle (the 'body')
        float minDistanceToPlayer = float.MaxValue;
        int count = ps.GetParticles(particlesArray);
        for (int i = 0; i < count; i++)
        {
            float dist = Vector3.Distance(playerTransform.position, particlesArray[i].position);
            if (dist < minDistanceToPlayer)
                minDistanceToPlayer = dist;
        }

        // If any part of the body is within 3 units, stop moving towards the player
        isCollidingWithPlayer = minDistanceToPlayer <= 3f;

        if (!isCollidingWithPlayer)
        {
            float clampedDeltaTime = Mathf.Min(Time.deltaTime, maxDeltaTime);
            // Calculate direction to player
            Vector3 attractionDirection = (playerTransform.position - transform.position).normalized;
            Vector3 attractionMovement = attractionDirection * attractionSpeed * clampedDeltaTime;
            transform.position += attractionMovement;

            // Clamp Y position to stay within boundaries
            Vector3 clampedPosition = transform.position;
            clampedPosition.y = Mathf.Clamp(clampedPosition.y, minY, maxY);
            transform.position = clampedPosition;
        }

        // Debug log to help diagnose collision issues
        if (isCollidingWithPlayer)
        {
//            Debug.Log($"SoulCreature is colliding with player at distance: {distanceToPlayer}");
        }
    }

    #endregion

    #region Player Movement Effects Implementation

    private void UpdatePlayerMovementEffects()
    {
        if (playerTransform == null || playerController == null) return;

        float clampedDeltaTime = Mathf.Min(Time.deltaTime, maxDeltaTime);

        // --- Manage Affected Speed Transition ---
        speedChangeTimer -= clampedDeltaTime;
        if (speedChangeTimer <= 0f)
        {
            targetAffectedSpeed = Random.Range(speedAffectedRateMin, speedAffectedRateMax); // Set new target speed
            speedLerpDuration = Random.Range(1f, 2f); // Set new lerp duration
            speedLerpTimer = 0f; // Reset lerp timer
            speedChangeTimer = Random.Range(1f, 5f); // Set time until next change
        }

        // Lerp current speed towards target speed
        speedLerpTimer += clampedDeltaTime;
        float t = Mathf.Clamp01(speedLerpTimer / speedLerpDuration);
        currentAffectedSpeed = Mathf.Lerp(currentAffectedSpeed, targetAffectedSpeed, t);

        Vector3 soulCenter = averageParticlePosition;
        float dist = Vector3.Distance(playerTransform.position, soulCenter);

        // Only affect if within range
        if (dist > maxRange) return;
        float affectStrength = Mathf.InverseLerp(maxRange, minRange, dist); // 0 at maxRange, 1 at minRange
        if (affectStrength <= 0f) return;

        // Get the player's movement direction from PlayerController
        Vector3 playerMoveDir = playerController.lastMoveDirection3D;
        if (playerMoveDir.sqrMagnitude > 0.0001f)
        {
            // Use the center of the soul creature (averageParticlePosition)
            Vector3 toSoul = (soulCenter - playerTransform.position).normalized;
            float approachDot = Vector3.Dot(playerMoveDir.normalized, toSoul);
            Vector3 moveDir;

            // If player is moving toward the soul creature (dot > sensitivity), move toward the player
            if (approachDot > playerApproachSensitivity)
            {
                moveDir = (playerTransform.position - soulCenter).normalized;
            }
            else
            {
                // Otherwise, mirror the player's movement direction
                moveDir = playerMoveDir.normalized;
            }

            transform.position += moveDir * currentAffectedSpeed * affectStrength * clampedDeltaTime;

            Vector3 clampedPosition = transform.position;
            clampedPosition.y = Mathf.Clamp(clampedPosition.y, minY, maxY);
            transform.position = clampedPosition;
        }
    }

    #endregion

    #region Audio Implementation

    private void UpdateAudio()
    {
        if (ps == null) return;

        // Get camera position for audio positioning
        Vector3 listenerPosition = Camera.main != null ? Camera.main.transform.position : Vector3.zero;

        int numParticlesAlive = ps.GetParticles(particlesArray);

        // Initialize custom data list if needed
        if (customDataList == null)
        {
            customDataList = new List<Vector4>(ps.main.maxParticles);
        }

        ps.GetCustomParticleData(customDataList, ParticleSystemCustomData.Custom1);
        float currentTime = Time.time;

        // Initial stagger for first play of sound1
        if (needsInitialDistribution && numParticlesAlive > 0)
        {
            for (int i = 0; i < numParticlesAlive; i++)
            {
                if (i >= customDataList.Count)
                {
                    customDataList.Add(new Vector4(currentTime + i * firstPlayStagger, 0, 0, 0));
                }
            }
            needsInitialDistribution = false;
        }

        // Process particles for audio
        for (int i = 0; i < numParticlesAlive; i++)
        {
            if (i >= customDataList.Count)
            {
                customDataList.Add(new Vector4(currentTime + Random.Range(minSound1Interval, maxSound1Interval), 0, 0, 0));
            }

            float nextSoundTime = customDataList[i].x;

            if (currentTime >= nextSoundTime)
            {
                if (!sound1.IsNull)
                {
                    // Use AudioManager's LOD-enabled one-shot method for ambient sounds
                    AudioManager.PlayOneShot(sound1, particlesArray[i].position);
                }

                // Set next sound time
                nextSoundTime = currentTime + Random.Range(minSound1Interval, maxSound1Interval);

                Vector4 data = customDataList[i];
                data.x = nextSoundTime;
                customDataList[i] = data;
            }
        }

        if (numParticlesAlive > 0)
        {
            ps.SetCustomParticleData(customDataList, ParticleSystemCustomData.Custom1);
        }
    }

    private void HandleAudioCollision(GameObject other)
    {
        if (ps == null) return;

        // Initialize collision events list if needed
        if (collisionEvents == null)
        {
            collisionEvents = new List<ParticleCollisionEvent>();
        }

        int numCollisionEvents = ps.GetCollisionEvents(other, collisionEvents);

        if (numCollisionEvents > 0 && Time.time >= lastCollisionSoundTime + collisionCooldown)
        {
            // Only process one collision event for performance
            int randomIndex = Random.Range(0, numCollisionEvents);
            Vector3 collisionPos = collisionEvents[randomIndex].intersection;

            if (!sound2.IsNull)
            {
                // Use AudioManager's LOD-enabled method for collision sounds
                AudioManager.PlayOneShot(sound2, collisionPos);
                lastCollisionSoundTime = Time.time;
            }
        }
    }
    #endregion
}