using UnityEngine;
using FMODUnity;

public class AmbientSoundSpawner : MonoBehaviour
{
    [SerializeField] private EventReference soundEvent;

    [Header("Settings")]
    [SerializeField] private Transform player;
    [SerializeField] private float triggerDistance = 18f;
    [SerializeField] private float soundRadius = 10f;
    [SerializeField] private float spawnRatePerSecond = 6f;

    private float timer;

    private float oceanBottomY;
    private float cloudsBottomY;
    private bool boundariesCached = false;

    private float updateInterval = 0.5f;
    private float updateTimer = 0f;

    private void CacheBoundaries()
    {
        if (boundariesCached) return;
        if (GameManager.Instance != null)
        {
            if (GameManager.Instance.oceanBottom != null)
                oceanBottomY = GameManager.Instance.oceanBottom.position.y;
            if (GameManager.Instance.cloudsBottom != null)
                cloudsBottomY = GameManager.Instance.cloudsBottom.position.y;
            boundariesCached = true;
        }
    }

    private void Update()
    {
        if (player == null) return;
        updateTimer += Time.deltaTime;
        if (updateTimer < updateInterval) return;
        updateTimer = 0f;
        CacheBoundaries();
        if (!boundariesCached) return;

        // Clamp Y between oceanBottom and cloudsBottom
        float clampedY = Mathf.Clamp(player.position.y, oceanBottomY, cloudsBottomY);
        Vector3 newPosition = new Vector3(player.position.x, clampedY, player.position.z);
        transform.position = newPosition;

        float distance = Vector3.Distance(player.position, transform.position);

        // --- Lowpass logic ---
        bool playerInOcean = false;
        var pc = player.GetComponent<PlayerController>();
        if (pc != null)
            playerInOcean = pc.isInsideOcean;

        // Set FMOD parameter for lowpass (assume parameter name "OceanLowpass", 0 = off, 1 = on)
        AudioManager.SetGlobalParameter("OceanLowpass", playerInOcean ? 1f : 0f);

        if (distance > triggerDistance) return;

        timer += updateInterval;
        if (timer >= 1f / spawnRatePerSecond)
        {
            timer = 0f;
            Vector2 randomCircle = Random.insideUnitCircle * soundRadius;
            Vector3 soundPos = new Vector3(transform.position.x + randomCircle.x, transform.position.y, transform.position.z + randomCircle.y);

            // Use bypass method to ensure ocean ambient sounds always play
            AudioManager.PlayOneShotBypassLOD(soundEvent, soundPos);
        }
    }
}
